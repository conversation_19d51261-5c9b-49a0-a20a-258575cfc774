<?php

namespace plugin\admin\app\model;

use plugin\admin\app\model\Base;

/**
 * @property integer $id 主键(主键)
 * @property string $region_id 地区唯一标识符（来源于API的id字段，通常为行政区划代码）
 * @property string $parent_id 父级地区ID（顶级地区为null）
 * @property integer $level 地区层级：1=国家，2=省/直辖市，3=市，4=区/县
 * @property string $name 地区中文名称
 * @property string $name_en 地区英文名称
 * @property string $name_ru 地区俄文名称
 * @property string $region_key 行政区划代码
 * @property string $region_value 地区值（通常与region_key相同）
 * @property integer $has_children 是否有下级地区
 * @property integer $is_enable 是否启用
 * @property integer $is_deleted 软删除标志
 * @property integer $sort_order 排序顺序
 * @property string $remark 备注信息
 * @property string $create_at 记录创建时间
 * @property string $update_at 记录更新时间
 */
class Region extends Base
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'regions';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    
    
}
