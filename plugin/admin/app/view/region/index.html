
<!DOCTYPE html>
<html lang="zh-cn">
    <head>
        <meta charset="utf-8">
        <title>浏览页面</title>
        <link rel="stylesheet" href="/app/admin/component/pear/css/pear.css" />
        <link rel="stylesheet" href="/app/admin/admin/css/reset.css" />
    </head>
    <body class="pear-container">
    
        <!-- 顶部查询表单 -->
        <div class="layui-card">
            <div class="layui-card-body">
                <form class="layui-form" action="">
                    <div class="layui-form-item">
                        <label class="layui-form-label">关键字</label>
                        <div class="layui-input-inline">
                            <input type="text" name="name" placeholder="请输入地区名称" class="layui-input">
                        </div>
                        <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="region-query">
                            <i class="layui-icon layui-icon-search"></i>
                            查询
                        </button>
                        <button type="reset" class="pear-btn pear-btn-md">
                            <i class="layui-icon layui-icon-refresh"></i>
                            重置
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="layui-card">
            <div class="layui-card-body">
                <table id="data-table" lay-filter="data-table"></table>
            </div>
        </div>

        <!-- 表格顶部工具栏 -->
        <script type="text/html" id="table-toolbar">
            <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add" permission="app.admin.region.insert">
                <i class="layui-icon layui-icon-add-1"></i>新增
            </button>
            <button class="pear-btn pear-btn-danger pear-btn-md" lay-event="batchRemove" permission="app.admin.region.delete">
                <i class="layui-icon layui-icon-delete"></i>删除
            </button>
            <button class="pear-btn pear-btn-success pear-btn-md" lay-event="expandAll">
                <i class="layui-icon layui-icon-spread-left"></i>展开全部
            </button>
            <button class="pear-btn pear-btn-success pear-btn-md" lay-event="foldAll">
                <i class="layui-icon layui-icon-shrink-right"></i>折叠全部
            </button>
            <button class="pear-btn pear-btn-success pear-btn-md" lay-event="reload">
                <i class="layui-icon layui-icon-refresh"></i>重载
            </button>
        </script>

        <!-- 表格行工具栏 -->
        <script type="text/html" id="table-bar">
            <button class="pear-btn pear-btn-primary pear-btn-xs" lay-event="edit" permission="app.admin.region.update">
                <i class="layui-icon layui-icon-edit"></i>编辑
            </button>
            <button class="pear-btn pear-btn-danger pear-btn-xs" lay-event="remove" permission="app.admin.region.delete">
                <i class="layui-icon layui-icon-delete"></i>删除
            </button>
        </script>

        <!-- 层级模板 -->
        <script type="text/html" id="level-tpl">
            {{# if (d.level == 1) { }}
                <span class="layui-badge layui-bg-blue">国家</span>
            {{# } else if (d.level == 2) { }}
                <span class="layui-badge layui-bg-green">省/直辖市</span>
            {{# } else if (d.level == 3) { }}
                <span class="layui-badge layui-bg-orange">市</span>
            {{# } else if (d.level == 4) { }}
                <span class="layui-badge layui-bg-red">区/县</span>
            {{# } else { }}
                <span class="layui-badge layui-bg-gray">{{d.level}}</span>
            {{# } }}
        </script>

        <!-- 启用状态模板 -->
        <script type="text/html" id="enable-tpl">
            {{# if (d.is_enable == 1) { }}
                <span style="color: green;">启用</span>
            {{# } else { }}
                <span style="color: red;">禁用</span>
            {{# } }}
        </script>

        <!-- 有下级模板 -->
        <script type="text/html" id="children-tpl">
            {{# if (d.has_children == 1) { }}
                <span style="color: green;">是</span>
            {{# } else { }}
                <span style="color: #999;">否</span>
            {{# } }}
        </script>

        <script src="/app/admin/component/layui/layui.js?v=2.8.12"></script>
        <script src="/app/admin/component/pear/pear.js"></script>
        <script src="/app/admin/admin/js/permission.js"></script>
        <script src="/app/admin/admin/js/common.js"></script>
        
        <script>

            // 相关常量
            const PRIMARY_KEY = "id";
            const SELECT_API = "/app/admin/region/select";
            const UPDATE_API = "/app/admin/region/update";
            const DELETE_API = "/app/admin/region/delete";
            const INSERT_URL = "/app/admin/region/insert";
            const UPDATE_URL = "/app/admin/region/update";

            // 表格渲染
            layui.use(["table", "form", "common", "popup", "util", "jquery", "treetable"], function() {
                let table = layui.table;
                let form = layui.form;
                let $ = layui.jquery;
                let common = layui.common;
                let util = layui.util;
                let treetable = layui.treetable;
                
				// 表头参数
				let cols = [
					[
						{type: "checkbox"},
						{field: "name", minWidth: 200, title: "地区名称"},
						{field: "region_id", title: "地区标识符", width: 150},
						{field: "level", title: "层级", width: 100, align: "center", templet: "#level-tpl"},
						{field: "region_key", title: "行政区划代码", width: 120},
						{field: "name_en", title: "英文名称", width: 150},
						{field: "name_ru", title: "俄文名称", width: 150},
						{field: "has_children", title: "有下级", width: 80, align: "center", templet: "#children-tpl"},
						{field: "is_enable", title: "启用", width: 80, align: "center", templet: "#enable-tpl"},
						{field: "sort_order", title: "排序", width: 80, align: "center"},
						{field: "create_at", title: "创建时间", width: 160},
						{title: "操作", toolbar: "#table-bar", width: 150, align: "center"}
					]
				];
				
				// 渲染表格
				function render()
				{
				    treetable.render({
				        treeColIndex: 1,          // 树形列索引（地区名称列，第0列是checkbox，第1列是name）
				        treeIdName: 'id',         // 树形id字段名（使用region_id）
				        treePidName: 'pid',       // 树形父id字段名
				        skin: 'line',
				        treeDefaultClose: true,   // 默认折叠
				        toolbar: '#table-toolbar',
				        elem: '#data-table',
				        url: SELECT_API + '?format=table_tree',
				        page: false,              // 树形表格不分页
				        cols: cols,
				        done: function(res, curr, count) {
				            // 表格渲染完成后的回调
				            console.log('Tree table rendered with', res.data ? res.data.length : 0, 'items');
				            console.log('First few items:', res.data ? res.data.slice(0, 3) : 'No data');
				        }
				    });
				}

				// 直接渲染表格
				render();
				
                // 编辑或删除行事件
                table.on("tool(data-table)", function(obj) {
                    if (obj.event === "remove") {
                        // 使用original_id（数据库真实id）进行删除操作
                        obj.data.id = obj.data.original_id || obj.data.id;
                        remove(obj);
                    } else if (obj.event === "edit") {
                        // 使用original_id（数据库真实id）进行编辑操作
                        obj.data.id = obj.data.original_id || obj.data.id;
                        edit(obj);
                    }
                });

                // 表格顶部工具栏事件
                table.on("toolbar(data-table)", function(obj) {
                    if (obj.event === "add") {
                        add();
                    } else if (obj.event === "refresh") {
                        refreshTable();
                    } else if (obj.event === "batchRemove") {
                        batchRemove(obj);
                    } else if (obj.event === "expandAll") {
                        console.log("Expanding all nodes...");
                        console.log("TreeTable container:", $("#data-table").next('.treeTable').length);
                        console.log("TreeTable icons:", $("#data-table").next('.treeTable').find('.treeTable-icon').length);
                        treetable.expandAll("#data-table");
                    } else if (obj.event === "foldAll") {
                        console.log("Folding all nodes...");
                        console.log("TreeTable container:", $("#data-table").next('.treeTable').length);
                        console.log("TreeTable icons:", $("#data-table").next('.treeTable').find('.treeTable-icon').length);
                        treetable.foldAll("#data-table");
                    } else if (obj.event === "reload") {
                        console.log("Reloading tree table...");
                        treetable.reload("#data-table");
                    }
                });

                // 表格顶部搜索事件
                form.on("submit(region-query)", function(data) {
                    var keyword = data.field.name;
                    if (keyword) {
                        treetable.search('#data-table', keyword);
                    } else {
                        treetable.reload("#data-table");
                    }
                    return false;
                });

                // 表格顶部搜索重置事件
                form.on("submit(table-reset)", function(data) {
                    treetable.reload("#data-table");
                    return false;
                });
                
                // 字段允许为空
                form.verify({
                    phone: [/(^$)|^1\d{10}$/, "请输入正确的手机号"],
                    email: [/(^$)|^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/, "邮箱格式不正确"],
                    url: [/(^$)|(^#)|(^http(s*):\/\/[^\s]+\.[^\s]+)/, "链接格式不正确"],
                    number: [/(^$)|^\d+$/,'只能填写数字'],
                    date: [/(^$)|^(\d{4})[-\/](\d{1}|0\d{1}|1[0-2])([-\/](\d{1}|0\d{1}|[1-2][0-9]|3[0-1]))*$/, "日期格式不正确"],
                    identity: [/(^$)|(^\d{15}$)|(^\d{17}(x|X|\d)$)/, "请输入正确的身份证号"]
                });

                // 表格排序事件
                table.on("sort(data-table)", function(obj){
                    table.reload("data-table", {
                        initSort: obj,
                        scrollPos: "fixed",
                        where: {
                            field: obj.field,
                            order: obj.type
                        }
                    });
                });

                // 表格新增数据
                let add = function() {
                    layer.open({
                        type: 2,
                        title: "新增",
                        shade: 0.1,
                        maxmin: true,
                        area: [common.isModile()?"100%":"500px", common.isModile()?"100%":"450px"],
                        content: INSERT_URL
                    });
                }

                // 表格编辑数据
                let edit = function(obj) {
                    let value = obj.data[PRIMARY_KEY];
                    layer.open({
                        type: 2,
                        title: "修改",
                        shade: 0.1,
                        maxmin: true,
                        area: [common.isModile()?"100%":"500px", common.isModile()?"100%":"450px"],
                        content: UPDATE_URL + "?" + PRIMARY_KEY + "=" + value
                    });
                }

                // 删除一行
                let remove = function(obj) {
                    return doRemove(obj.data[PRIMARY_KEY]);
                }

                // 删除多行
                let batchRemove = function(obj) {
                    let checkIds = common.checkField(obj, PRIMARY_KEY);
                    if (checkIds === "") {
                        layui.popup.warning("未选中数据");
                        return false;
                    }
                    doRemove(checkIds.split(","));
                }

                // 执行删除
                let doRemove = function (ids) {
                    let data = {};
                    data[PRIMARY_KEY] = ids;
                    layer.confirm("确定删除?", {
                        icon: 3,
                        title: "提示"
                    }, function(index) {
                        layer.close(index);
                        let loading = layer.load();
                        $.ajax({
                            url: DELETE_API,
                            data: data,
                            dataType: "json",
                            type: "post",
                            success: function(res) {
                                layer.close(loading);
                                if (res.code) {
                                    return layui.popup.failure(res.msg);
                                }
                                return layui.popup.success("操作成功", refreshTable);
                            }
                        })
                    });
                }

                // 刷新表格数据
                window.refreshTable = function() {
                    treetable.reload("#data-table");
                }
            })

        </script>
    </body>
</html>
