<!DOCTYPE html>
<html lang="zh-cn">
    <head>
        <meta charset="UTF-8">
        <title>更新页面</title>
        <link rel="stylesheet" href="/app/admin/component/pear/css/pear.css" />
        <link rel="stylesheet" href="/app/admin/component/jsoneditor/css/jsoneditor.css" />
        <link rel="stylesheet" href="/app/admin/admin/css/reset.css" />
        
    </head>
    <body>

        <form class="layui-form">

            <div class="mainBox">
                <div class="main-container mr-5">
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label required">地区唯一标识符（来源于API的id字段，通常为行政区划代码）</label>
                        <div class="layui-input-block">
                            <input type="text" name="region_id" value="" required lay-verify="required" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">父级地区ID（顶级地区为null）</label>
                        <div class="layui-input-block">
                            <input type="text" name="parent_id" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label required">地区层级：1=国家，2=省/直辖市，3=市，4=区/县</label>
                        <div class="layui-input-block">
                            <input type="number" name="level" value="" required lay-verify="required" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label required">地区中文名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="name" value="" required lay-verify="required" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">地区英文名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="name_en" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">地区俄文名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="name_ru" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">行政区划代码</label>
                        <div class="layui-input-block">
                            <input type="text" name="region_key" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">地区值（通常与region_key相同）</label>
                        <div class="layui-input-block">
                            <input type="text" name="region_value" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">是否有下级地区</label>
                        <div class="layui-input-block">
                            <div name="has_children" id="has_children" value="" ></div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">是否启用</label>
                        <div class="layui-input-block">
                            <div name="is_enable" id="is_enable" value="" ></div>
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">排序顺序</label>
                        <div class="layui-input-block">
                            <input type="number" name="sort_order" value="" class="layui-input">
                        </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label">备注信息</label>
                        <div class="layui-input-block">
                            <textarea name="remark" class="layui-textarea"></textarea>
                        </div>
                    </div>
                    
                </div>
            </div>

            <div class="bottom">
                <div class="button-container">
                    <button type="submit" class="pear-btn pear-btn-primary pear-btn-md" lay-submit="" lay-filter="save">
                        提交
                    </button>
                    <button type="reset" class="pear-btn pear-btn-md">
                        重置
                    </button>
                </div>
            </div>
            
        </form>

        <script src="/app/admin/component/layui/layui.js?v=2.8.12"></script>
        <script src="/app/admin/component/pear/pear.js"></script>
        <script src="/app/admin/component/jsoneditor/jsoneditor.js"></script>
        <script src="/app/admin/admin/js/permission.js"></script>
        
        <script>

            // 相关接口
            const PRIMARY_KEY = "id";
            const SELECT_API = "/app/admin/region/select" + location.search;
            const UPDATE_API = "/app/admin/region/update";
            // 获取数据库记录
            layui.use(["form", "util", "popup"], function () {
                let $ = layui.$;
                $.ajax({
                    url: SELECT_API,
                    dataType: "json",
                    success: function (res) {
                        
                        // 给表单初始化数据
                        layui.each(res.data[0], function (key, value) {
                            let obj = $('*[name="'+key+'"]');
                            if (key === "password") {
                                obj.attr("placeholder", "不更新密码请留空");
                                return;
                            }
                            if (typeof obj[0] === "undefined" || !obj[0].nodeName) return;
                            if (obj[0].nodeName.toLowerCase() === "textarea") {
                                obj.val(value);
                            } else {
                                obj.attr("value", value);
                                obj[0].value = value;
                            }
                            
                            // 多图渲染
                            if (obj[0].classList.contains('uploader-list')) {
                                let multiple_images = value.split(",");
                                $.each(multiple_images, function(index, value) {
                                    $('#uploader-list-'+ key).append(
                                        '<div class="file-iteme">' +
                                        '<div class="handle"><i class="layui-icon layui-icon-delete"></i></div>' +
                                        '<img src='+value +' alt="'+ value +'" >' +
                                        '</div>'
                                    );
                                });
                            }
                        });
                        
                        // 字段 是否有下级地区 has_children
                        layui.use(["jquery", "xmSelect", "popup"], function() {
                            layui.$.ajax({
                                url: "/app/admin/dict/get/whether",
                                dataType: "json",
                                success: function (res) {
                                    let value = layui.$("#has_children").attr("value");
                                    let initValue = value ? value.split(",") : [];
                                    layui.xmSelect.render({
                                        el: "#has_children",
                                        name: "has_children",
                                        initValue: initValue,
                                        filterable: true,
                                        data: res.data, 
                                        model: {"icon":"hidden","label":{"type":"text"}},
                                        clickClose: true,
                                        radio: true,
                                    });
                                    if (res.code) {
                                        layui.popup.failure(res.msg);
                                    }
                                }
                            });
                        });
                        
                        // 字段 是否启用 is_enable
                        layui.use(["jquery", "xmSelect", "popup"], function() {
                            layui.$.ajax({
                                url: "/app/admin/dict/get/whether",
                                dataType: "json",
                                success: function (res) {
                                    let value = layui.$("#is_enable").attr("value");
                                    let initValue = value ? value.split(",") : [];
                                    layui.xmSelect.render({
                                        el: "#is_enable",
                                        name: "is_enable",
                                        initValue: initValue,
                                        filterable: true,
                                        data: res.data, 
                                        model: {"icon":"hidden","label":{"type":"text"}},
                                        clickClose: true,
                                        radio: true,
                                    });
                                    if (res.code) {
                                        layui.popup.failure(res.msg);
                                    }
                                }
                            });
                        });
                        
                        
                        // ajax返回失败
                        if (res.code) {
                            layui.popup.failure(res.msg);
                        }
                        
                    }
                });
            });

            //提交事件
            layui.use(["form", "popup"], function () {
                // 字段验证允许为空
                layui.form.verify({
                    phone: [/(^$)|^1\d{10}$/, "请输入正确的手机号"],
                    email: [/(^$)|^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/, "邮箱格式不正确"],
                    url: [/(^$)|(^#)|(^http(s*):\/\/[^\s]+\.[^\s]+)/, "链接格式不正确"],
                    number: [/(^$)|^\d+$/,'只能填写数字'],
                    date: [/(^$)|^(\d{4})[-\/](\d{1}|0\d{1}|1[0-2])([-\/](\d{1}|0\d{1}|[1-2][0-9]|3[0-1]))*$/, "日期格式不正确"],
                    identity: [/(^$)|(^\d{15}$)|(^\d{17}(x|X|\d)$)/, "请输入正确的身份证号"]
                });
                layui.form.on("submit(save)", function (data) {
                    data.field[PRIMARY_KEY] = layui.url().search[PRIMARY_KEY];
                    layui.$.ajax({
                        url: UPDATE_API,
                        type: "POST",
                        dateType: "json",
                        data: data.field,
                        success: function (res) {
                            if (res.code) {
                                return layui.popup.failure(res.msg);
                            }
                            return layui.popup.success("操作成功", function () {
                                parent.refreshTable();
                                parent.layer.close(parent.layer.getFrameIndex(window.name));
                            });
                        }
                    });
                    return false;
                });
            });

        </script>

    </body>

</html>
