<?php

namespace plugin\admin\app\controller;

use support\Request;
use support\Response;
use plugin\admin\app\model\Region;
use plugin\admin\app\controller\Crud;
use support\exception\BusinessException;

/**
 * 地区数据管理 
 */
class RegionController extends Crud
{
    
    /**
     * @var Region
     */
    protected $model = null;

    /**
     * 构造函数
     * @return void
     */
    public function __construct()
    {
        $this->model = new Region;
    }
    
    /**
     * 浏览
     * @return Response
     */
    public function index(): Response
    {
        return view('region/index');
    }

    /**
     * 插入
     * @param Request $request
     * @return Response
     * @throws BusinessException
     */
    public function insert(Request $request): Response
    {
        if ($request->method() === 'POST') {
            return parent::insert($request);
        }
        return view('region/insert');
    }

    /**
     * 更新
     * @param Request $request
     * @return Response
     * @throws BusinessException
    */
    public function update(Request $request): Response
    {
        if ($request->method() === 'POST') {
            return parent::update($request);
        }
        return view('region/update');
    }

    /**
     * 重写doFormat方法以支持树形表格
     * @param $query
     * @param $format
     * @param $limit
     * @return Response
     */
    protected function doFormat($query, $format, $limit): Response
    {
        if ($format === 'table_tree') {
            // 对于树形表格，获取所有数据不分页
            $items = $query->get()->toArray();

            // treetable组件需要平铺的数据结构，不是嵌套的树结构
            // 只需要调整字段映射即可
            $treeData = [];
            foreach ($items as $item) {
                $treeItem = $item;
                $treeItem['id'] = $item['region_id']; // 使用region_id作为树节点id
                $treeItem['pid'] = $item['parent_id'] ?: null; // parent_id为null时保持null，不设为'0'
                $treeItem['original_id'] = $item['id']; // 保存原始数据库id用于操作
                $treeData[] = $treeItem;
            }

            return $this->json(0, 'ok', $treeData);
        }

        return parent::doFormat($query, $format, $limit);
    }

}
